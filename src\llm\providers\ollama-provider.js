/**
 * Ollama LLM Provider
 * Implements local Ollama API integration
 */

import axios from 'axios';
import { BaseLLMProvider } from './base-provider.js';

export class OllamaProvider extends BaseLLMProvider {
  #httpClient;
  #baseUrl;
  #models;

  constructor(config, logger) {
    super('ollama', config, logger);
    
    const providerConfig = this.getProviderConfig();
    this.#baseUrl = providerConfig.baseUrl;
    this.#models = providerConfig.models;
    
    this.#initializeHttpClient();
  }

  async initialize() {
    this.log('info', 'Initializing Ollama provider');

    try {
      await this.checkAvailability();
      this.setAvailable(true);
      this.log('info', 'Ollama provider initialized successfully');
    } catch (error) {
      this.log('warn', 'Ollama provider unavailable', {
        error: error.message,
        suggestion: 'Make sure Ollama is installed and running. Visit https://ollama.ai for installation instructions.'
      });
      this.setAvailable(false);
    }
  }

  async checkAvailability() {
    try {
      this.log('debug', 'Checking Ollama API availability');
      const response = await this.#httpClient.get('/api/tags');

      if (response.status === 200) {
        this.log('debug', 'Ollama API is available');
        return true;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      this.log('debug', 'Ollama API availability check failed', {
        status: error.response?.status,
        message: error.message,
        code: error.code
      });

      if (error.code === 'ECONNREFUSED') {
        throw new Error('Ollama service is not running. Please start Ollama with: ollama serve');
      }
      if (error.code === 'ENOTFOUND') {
        throw new Error('Cannot connect to Ollama. Please check if Ollama is installed and the baseUrl is correct.');
      }
      if (error.response?.status === 404) {
        throw new Error('Ollama API endpoint not found. Please check the baseUrl configuration.');
      }

      throw new Error(`Ollama API unavailable: ${error.message}`);
    }
  }

  async generateCompletion(messages, options = {}) {
    this.validateMessages(messages);
    
    const model = options.model || this.#models.default;
    const prompt = this.#convertMessagesToPrompt(messages);
    
    const requestData = {
      model,
      prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 1,
        top_k: options.top_k || 40,
        num_predict: options.max_tokens || 4096
      }
    };

    this.log('debug', 'Generating completion', { 
      model, 
      promptLength: prompt.length,
      options: requestData.options 
    });

    try {
      const response = await this.#httpClient.post('/api/generate', requestData);
      
      const content = response.data.response || '';
      
      this.log('debug', 'Completion generated successfully', { 
        contentLength: content.length,
        done: response.data.done
      });

      return this.formatSuccess({
        content,
        model,
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(content),
          total_tokens: this.estimateTokens(prompt + content)
        },
        finishReason: response.data.done ? 'stop' : 'length'
      });

    } catch (error) {
      this.log('error', 'Failed to generate completion', { error: error.message });
      throw error;
    }
  }

  async generateCompletionWithTools(messages, tools, options = {}) {
    // Ollama doesn't natively support function calling, so we'll simulate it
    // by including tool descriptions in the system prompt
    
    const toolDescriptions = this.#formatToolsForPrompt(tools);
    const systemMessage = {
      role: 'system',
      content: `You are an AI assistant with access to the following tools:

${toolDescriptions}

When you need to use a tool, respond with a JSON object in this format:
{
  "tool_call": {
    "name": "tool_name",
    "arguments": { "param1": "value1", "param2": "value2" }
  }
}

If you don't need to use a tool, respond normally with text.`
    };

    const enhancedMessages = [systemMessage, ...messages];
    
    const result = await this.generateCompletion(enhancedMessages, options);
    
    if (result.success) {
      // Try to parse tool calls from the response
      const toolCalls = this.#parseToolCallsFromContent(result.data.content);
      
      if (toolCalls.length > 0) {
        result.data.toolCalls = toolCalls;
        result.data.content = ''; // Clear content if we found tool calls
      }
    }
    
    return result;
  }

  async streamCompletion(messages, options = {}) {
    this.validateMessages(messages);
    
    const model = options.model || this.#models.default;
    const prompt = this.#convertMessagesToPrompt(messages);
    
    const requestData = {
      model,
      prompt,
      stream: true,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 1,
        top_k: options.top_k || 40,
        num_predict: options.max_tokens || 4096
      }
    };

    this.log('debug', 'Starting streaming completion', { 
      model, 
      promptLength: prompt.length 
    });

    try {
      const response = await this.#httpClient.post('/api/generate', requestData, {
        responseType: 'stream'
      });

      return this.#createStreamIterator(response.data);

    } catch (error) {
      this.log('error', 'Failed to start streaming completion', { error: error.message });
      throw error;
    }
  }

  async getAvailableModels() {
    try {
      const response = await this.#httpClient.get('/api/tags');
      
      const models = response.data.models.map(model => ({
        id: model.name,
        name: model.name,
        description: `${model.name} (${this.#formatSize(model.size)})`,
        contextLength: 4096, // Default, Ollama doesn't provide this info
        size: model.size,
        modified: model.modified_at
      }));

      this.log('debug', 'Retrieved available models', { count: models.length });
      
      return models;

    } catch (error) {
      this.log('error', 'Failed to get available models', { error: error.message });
      throw error;
    }
  }

  async pullModel(modelName) {
    this.log('info', `Pulling model: ${modelName}`);
    
    try {
      const response = await this.#httpClient.post('/api/pull', {
        name: modelName,
        stream: false
      });

      this.log('info', `Model pulled successfully: ${modelName}`);
      return response.data;

    } catch (error) {
      this.log('error', `Failed to pull model: ${modelName}`, { error: error.message });
      throw error;
    }
  }

  async deleteModel(modelName) {
    this.log('info', `Deleting model: ${modelName}`);
    
    try {
      const response = await this.#httpClient.delete('/api/delete', {
        data: { name: modelName }
      });

      this.log('info', `Model deleted successfully: ${modelName}`);
      return response.data;

    } catch (error) {
      this.log('error', `Failed to delete model: ${modelName}`, { error: error.message });
      throw error;
    }
  }

  // Private methods

  #initializeHttpClient() {
    const providerConfig = this.getProviderConfig();
    
    this.#httpClient = axios.create({
      baseURL: this.#baseUrl,
      timeout: providerConfig.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor
    this.#httpClient.interceptors.request.use(
      (config) => {
        this.log('debug', 'Making Ollama API request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          dataSize: config.data ? JSON.stringify(config.data).length : 0
        });
        return config;
      },
      (error) => {
        this.log('error', 'Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.#httpClient.interceptors.response.use(
      (response) => {
        this.log('debug', 'Ollama API request completed', {
          status: response.status,
          url: response.config.url
        });
        return response;
      },
      (error) => {
        this.log('error', 'Ollama API request failed', {
          status: error.response?.status,
          url: error.config?.url,
          error: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  #convertMessagesToPrompt(messages) {
    // Convert OpenAI-style messages to a single prompt for Ollama
    let prompt = '';

    for (const message of messages) {
      switch (message.role) {
        case 'system':
          prompt += `System: ${message.content}\n\n`;
          break;
        case 'user':
          prompt += `Human: ${message.content}\n\n`;
          break;
        case 'assistant':
          // Handle assistant messages with tool calls
          if (message.tool_calls && message.tool_calls.length > 0) {
            prompt += `Assistant: ${message.content || 'I need to use some tools.'}\n`;
            for (const toolCall of message.tool_calls) {
              prompt += `Tool Call: ${toolCall.function.name}(${JSON.stringify(toolCall.function.arguments)})\n`;
            }
            prompt += '\n';
          } else {
            prompt += `Assistant: ${message.content}\n\n`;
          }
          break;
        case 'function':
          prompt += `Tool Result (${message.name}): ${message.content}\n\n`;
          break;
      }
    }

    prompt += 'Assistant: ';
    return prompt;
  }

  #formatToolsForPrompt(tools) {
    return tools.map(tool => {
      const func = tool.function;
      return `- ${func.name}: ${func.description}
  Parameters: ${JSON.stringify(func.parameters, null, 2)}`;
    }).join('\n\n');
  }

  #parseToolCallsFromContent(content) {
    const toolCalls = [];
    
    try {
      // Try to find JSON objects in the content
      const jsonMatch = content.match(/\{[\s\S]*"tool_call"[\s\S]*\}/);
      
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        if (parsed.tool_call && parsed.tool_call.name) {
          toolCalls.push({
            id: `call_${Date.now()}`,
            type: 'function',
            function: {
              name: parsed.tool_call.name,
              arguments: parsed.tool_call.arguments || {}
            }
          });
        }
      }
    } catch (error) {
      this.log('debug', 'Failed to parse tool calls from content', { error: error.message });
    }
    
    return toolCalls;
  }

  async *#createStreamIterator(stream) {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() === '') continue;
        
        try {
          const parsed = JSON.parse(line);
          
          if (parsed.response) {
            yield {
              content: parsed.response,
              finishReason: parsed.done ? 'stop' : null,
              done: parsed.done
            };
          }
          
          if (parsed.done) {
            return;
          }
        } catch (error) {
          this.log('warn', 'Failed to parse streaming chunk', { error: error.message });
        }
      }
    }
  }

  #formatSize(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}
