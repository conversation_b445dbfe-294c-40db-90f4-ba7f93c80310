/**
 * LLM Client - Manages multiple LLM providers with fallback support
 */

import { DeepseekProvider } from './providers/deepseek-provider.js';
import { OllamaProvider } from './providers/ollama-provider.js';

export class LLMClient {
  #config;
  #logger;
  #providers = new Map();
  #defaultProvider;
  #fallbackProvider;

  constructor(config, logger) {
    this.#config = config;
    this.#logger = logger;
  }

  async initialize() {
    this.#logger.info('Initializing LLM client');

    // Initialize providers
    await this.#initializeProviders();

    // Set default and fallback providers
    const llmConfig = this.#config.get('llm');
    this.#defaultProvider = llmConfig.defaultProvider;
    this.#fallbackProvider = llmConfig.fallbackProvider;

    // Validate configuration
    this.#validateConfiguration();

    this.#logger.info('LLM client initialized', {
      providers: Array.from(this.#providers.keys()),
      defaultProvider: this.#defaultProvider,
      fallbackProvider: this.#fallbackProvider,
      availableProviders: this.getAvailableProviders()
    });
  }

  async #initializeProviders() {
    // Initialize Deepseek provider
    if (this.#config.isProviderEnabled('deepseek')) {
      const deepseek = new DeepseekProvider(this.#config, this.#logger);
      await deepseek.initialize();
      this.#providers.set('deepseek', deepseek);
    }

    // Initialize Ollama provider
    if (this.#config.isProviderEnabled('ollama')) {
      const ollama = new OllamaProvider(this.#config, this.#logger);
      await ollama.initialize();
      this.#providers.set('ollama', ollama);
    }
  }

  /**
   * Generate completion with automatic provider selection and fallback
   */
  async generateCompletion(messages, options = {}) {
    // Validate input
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages must be a non-empty array');
    }

    const provider = options.provider || this.#defaultProvider;

    // Log request details for debugging
    this.#logger.debug('Generating completion', {
      provider,
      messageCount: messages.length,
      hasOptions: Object.keys(options).length > 0,
      messageRoles: messages.map(m => m.role)
    });

    // Check if primary provider is available
    if (!this.isProviderAvailable(provider)) {
      this.#logger.warn(`Primary provider ${provider} is not available, trying fallback immediately`);

      if (this.#fallbackProvider && this.#fallbackProvider !== provider && this.isProviderAvailable(this.#fallbackProvider)) {
        return await this.#executeWithProvider(this.#fallbackProvider, 'generateCompletion', messages, options);
      } else {
        throw new Error(`No available providers. Primary provider '${provider}' is unavailable and no working fallback found.`);
      }
    }

    try {
      return await this.#executeWithProvider(provider, 'generateCompletion', messages, options);
    } catch (error) {
      this.#logger.warn(`Primary provider ${provider} failed, trying fallback`, {
        error: error.message,
        messageCount: messages.length
      });

      if (this.#fallbackProvider && this.#fallbackProvider !== provider && this.isProviderAvailable(this.#fallbackProvider)) {
        try {
          return await this.#executeWithProvider(this.#fallbackProvider, 'generateCompletion', messages, options);
        } catch (fallbackError) {
          this.#logger.error('Fallback provider also failed', {
            error: fallbackError.message
          });
          throw new Error(`All providers failed. Primary (${provider}): ${error.message}, Fallback (${this.#fallbackProvider}): ${fallbackError.message}`);
        }
      }

      // Provide helpful error message if no fallback is available
      if (!this.#fallbackProvider) {
        throw new Error(`Provider '${provider}' failed and no fallback provider is configured: ${error.message}`);
      } else if (this.#fallbackProvider === provider) {
        throw new Error(`Provider '${provider}' failed and fallback is the same provider: ${error.message}`);
      } else if (!this.isProviderAvailable(this.#fallbackProvider)) {
        throw new Error(`Provider '${provider}' failed and fallback provider '${this.#fallbackProvider}' is not available: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Generate completion with function calling
   */
  async generateCompletionWithTools(messages, tools, options = {}) {
    // Validate input
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new Error('Messages must be a non-empty array');
    }
    if (!Array.isArray(tools) || tools.length === 0) {
      throw new Error('Tools must be a non-empty array');
    }

    const provider = options.provider || this.#defaultProvider;

    // Log request details for debugging
    this.#logger.debug('Generating completion with tools', {
      provider,
      messageCount: messages.length,
      toolCount: tools.length,
      toolNames: tools.map(t => t.function?.name).filter(Boolean),
      messageRoles: messages.map(m => m.role)
    });

    try {
      return await this.#executeWithProvider(provider, 'generateCompletionWithTools', messages, tools, options);
    } catch (error) {
      this.#logger.warn(`Primary provider ${provider} failed for tool completion, trying fallback`, {
        error: error.message,
        messageCount: messages.length,
        toolCount: tools.length
      });

      if (this.#fallbackProvider && this.#fallbackProvider !== provider && this.isProviderAvailable(this.#fallbackProvider)) {
        try {
          return await this.#executeWithProvider(this.#fallbackProvider, 'generateCompletionWithTools', messages, tools, options);
        } catch (fallbackError) {
          this.#logger.error('Fallback provider also failed for tool completion', {
            error: fallbackError.message
          });
          throw new Error(`All providers failed. Primary: ${error.message}, Fallback: ${fallbackError.message}`);
        }
      }

      throw error;
    }
  }

  /**
   * Stream completion
   */
  async streamCompletion(messages, options = {}) {
    const provider = options.provider || this.#defaultProvider;
    return await this.#executeWithProvider(provider, 'streamCompletion', messages, options);
  }

  /**
   * Get available models from all providers
   */
  async getAvailableModels() {
    const allModels = {};
    
    for (const [name, provider] of this.#providers) {
      if (provider.isAvailable()) {
        try {
          const models = await provider.getAvailableModels();
          allModels[name] = models;
        } catch (error) {
          this.#logger.warn(`Failed to get models from ${name}`, { error: error.message });
          allModels[name] = [];
        }
      }
    }
    
    return allModels;
  }

  /**
   * Get provider status
   */
  getProviderStatus() {
    const status = {};
    
    for (const [name, provider] of this.#providers) {
      status[name] = provider.getStatus();
    }
    
    return status;
  }

  /**
   * Get specific provider
   */
  getProvider(name) {
    return this.#providers.get(name);
  }

  /**
   * Check if provider is available
   */
  isProviderAvailable(name) {
    const provider = this.#providers.get(name);
    return provider ? provider.isAvailable() : false;
  }

  /**
   * Get list of available providers
   */
  getAvailableProviders() {
    return Array.from(this.#providers.entries())
      .filter(([, provider]) => provider.isAvailable())
      .map(([name]) => name);
  }

  /**
   * Switch default provider
   */
  setDefaultProvider(providerName) {
    if (!this.#providers.has(providerName)) {
      throw new Error(`Provider not found: ${providerName}`);
    }
    
    if (!this.isProviderAvailable(providerName)) {
      throw new Error(`Provider not available: ${providerName}`);
    }
    
    this.#defaultProvider = providerName;
    this.#config.set('llm.defaultProvider', providerName);
    
    this.#logger.info(`Default provider changed to: ${providerName}`);
  }

  /**
   * Test provider connectivity
   */
  async testProvider(providerName) {
    const provider = this.#providers.get(providerName);
    
    if (!provider) {
      throw new Error(`Provider not found: ${providerName}`);
    }
    
    try {
      const testMessages = [
        { role: 'user', content: 'Hello, this is a connectivity test. Please respond with "OK".' }
      ];
      
      const startTime = Date.now();
      const result = await provider.generateCompletion(testMessages, { max_tokens: 10 });
      const duration = Date.now() - startTime;
      
      return {
        success: result.success,
        provider: providerName,
        responseTime: duration,
        response: result.data?.content || 'No response'
      };
      
    } catch (error) {
      return {
        success: false,
        provider: providerName,
        error: error.message
      };
    }
  }

  /**
   * Test all providers
   */
  async testAllProviders() {
    const results = {};
    
    for (const [name] of this.#providers) {
      if (this.isProviderAvailable(name)) {
        results[name] = await this.testProvider(name);
      } else {
        results[name] = {
          success: false,
          provider: name,
          error: 'Provider not available'
        };
      }
    }
    
    return results;
  }

  /**
   * Get usage statistics
   */
  getUsageStats() {
    // This would be implemented with proper usage tracking
    return {
      totalRequests: 0,
      totalTokens: 0,
      providerUsage: {},
      errorRate: 0
    };
  }

  // Private methods

  async #executeWithProvider(providerName, method, ...args) {
    const provider = this.#providers.get(providerName);
    
    if (!provider) {
      throw new Error(`Provider not found: ${providerName}`);
    }
    
    if (!provider.isAvailable()) {
      throw new Error(`Provider not available: ${providerName}`);
    }
    
    this.#logger.debug(`Executing ${method} with provider: ${providerName}`);
    
    const startTime = Date.now();
    try {
      const result = await provider[method](...args);
      const duration = Date.now() - startTime;
      
      this.#logger.debug(`Provider execution completed: ${providerName}`, {
        method,
        duration,
        success: result.success
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.#logger.error(`Provider execution failed: ${providerName}`, {
        method,
        duration,
        error: error.message
      });
      
      throw error;
    }
  }

  // Private validation method
  #validateConfiguration() {
    const availableProviders = this.getAvailableProviders();

    if (availableProviders.length === 0) {
      this.#logger.warn('No LLM providers are available. Please check your configuration and API keys.');
      return;
    }

    if (!availableProviders.includes(this.#defaultProvider)) {
      this.#logger.warn(`Default provider '${this.#defaultProvider}' is not available. Available providers: ${availableProviders.join(', ')}`);
    }

    if (this.#fallbackProvider && !availableProviders.includes(this.#fallbackProvider)) {
      this.#logger.warn(`Fallback provider '${this.#fallbackProvider}' is not available. Available providers: ${availableProviders.join(', ')}`);
    }

    if (availableProviders.length === 1 && this.#defaultProvider === this.#fallbackProvider) {
      this.#logger.info('Only one provider is available and configured as both default and fallback.');
    }
  }
}
