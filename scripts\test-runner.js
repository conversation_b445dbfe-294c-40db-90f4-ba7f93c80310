#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Arien-AI
 * Runs different types of tests with proper setup and cleanup
 */

import { spawn } from 'child_process';
import { existsSync, rmSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import chalk from 'chalk';

class TestRunner {
  constructor() {
    this.testResults = {
      unit: { passed: 0, failed: 0, total: 0 },
      integration: { passed: 0, failed: 0, total: 0 },
      e2e: { passed: 0, failed: 0, total: 0 }
    };
    this.startTime = Date.now();
  }

  async run() {
    console.log(chalk.blue.bold('🧪 Arien-AI Test Suite Runner\n'));

    try {
      // Setup test environment
      await this.setupTestEnvironment();

      // Run different test suites
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runE2ETests();

      // Show summary
      this.showSummary();

    } catch (error) {
      console.error(chalk.red('❌ Test runner failed:'), error.message);
      process.exit(1);
    } finally {
      // Cleanup
      await this.cleanup();
    }
  }

  async setupTestEnvironment() {
    console.log(chalk.yellow('🔧 Setting up test environment...'));

    // Clean up any existing test directories
    const testDirs = [
      join(homedir(), '.arien-ai-test'),
      join(homedir(), '.arien-ai-test-logs'),
      join(homedir(), '.arien-ai-test-integration'),
      join(homedir(), '.arien-ai-test-e2e')
    ];

    for (const dir of testDirs) {
      if (existsSync(dir)) {
        rmSync(dir, { recursive: true, force: true });
      }
    }

    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.ARIEN_TEST_MODE = 'true';
    
    console.log(chalk.green('✅ Test environment ready\n'));
  }

  async runUnitTests() {
    console.log(chalk.blue('📋 Running Unit Tests...'));
    
    const result = await this.runTestCommand([
      '--test',
      'tests/unit/*.test.js'
    ]);

    this.parseTestResults(result, 'unit');
  }

  async runIntegrationTests() {
    console.log(chalk.blue('🔗 Running Integration Tests...'));
    
    const result = await this.runTestCommand([
      '--test',
      'tests/integration/*.test.js'
    ]);

    this.parseTestResults(result, 'integration');
  }

  async runE2ETests() {
    console.log(chalk.blue('🎯 Running End-to-End Tests...'));
    
    const result = await this.runTestCommand([
      '--test',
      'tests/e2e/*.test.js'
    ]);

    this.parseTestResults(result, 'e2e');
  }

  async runTestCommand(args) {
    return new Promise((resolve) => {
      const child = spawn('node', args, {
        stdio: 'pipe',
        env: {
          ...process.env,
          FORCE_COLOR: '1' // Enable colors in test output
        }
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        process.stdout.write(output);
      });

      child.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        process.stderr.write(output);
      });

      child.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr
        });
      });
    });
  }

  parseTestResults(result, testType) {
    const output = result.stdout + result.stderr;
    
    // Parse Node.js test runner output
    const passMatch = output.match(/ℹ pass (\d+)/);
    const failMatch = output.match(/ℹ fail (\d+)/);
    const totalMatch = output.match(/ℹ tests (\d+)/);

    if (passMatch) this.testResults[testType].passed = parseInt(passMatch[1]);
    if (failMatch) this.testResults[testType].failed = parseInt(failMatch[1]);
    if (totalMatch) this.testResults[testType].total = parseInt(totalMatch[1]);

    const status = this.testResults[testType].failed === 0 ? 
      chalk.green('✅ PASSED') : 
      chalk.red('❌ FAILED');

    console.log(`${status} ${testType.toUpperCase()} Tests: ${this.testResults[testType].passed}/${this.testResults[testType].total}\n`);
  }

  showSummary() {
    const duration = Date.now() - this.startTime;
    const totalPassed = Object.values(this.testResults).reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = Object.values(this.testResults).reduce((sum, r) => sum + r.failed, 0);
    const totalTests = Object.values(this.testResults).reduce((sum, r) => sum + r.total, 0);

    console.log(chalk.blue.bold('📊 Test Summary'));
    console.log(chalk.gray('─'.repeat(50)));
    
    // Individual test type results
    for (const [type, results] of Object.entries(this.testResults)) {
      const status = results.failed === 0 ? '✅' : '❌';
      console.log(`${status} ${type.padEnd(12)}: ${results.passed}/${results.total} passed`);
    }

    console.log(chalk.gray('─'.repeat(50)));
    
    // Overall results
    const overallStatus = totalFailed === 0 ? 
      chalk.green.bold('✅ ALL TESTS PASSED') : 
      chalk.red.bold('❌ SOME TESTS FAILED');

    console.log(`${overallStatus}`);
    console.log(`Total: ${totalPassed}/${totalTests} tests passed`);
    console.log(`Duration: ${(duration / 1000).toFixed(2)}s`);

    if (totalFailed > 0) {
      console.log(chalk.yellow('\n💡 Tips for fixing failed tests:'));
      console.log('• Check if all dependencies are installed');
      console.log('• Ensure API keys are configured (for integration tests)');
      console.log('• Verify system permissions');
      console.log('• Check if ports are available (for E2E tests)');
    }

    // Exit with appropriate code
    process.exit(totalFailed > 0 ? 1 : 0);
  }

  async cleanup() {
    console.log(chalk.yellow('\n🧹 Cleaning up test environment...'));

    // Remove test directories
    const testDirs = [
      join(homedir(), '.arien-ai-test'),
      join(homedir(), '.arien-ai-test-logs'),
      join(homedir(), '.arien-ai-test-integration'),
      join(homedir(), '.arien-ai-test-e2e')
    ];

    for (const dir of testDirs) {
      if (existsSync(dir)) {
        try {
          rmSync(dir, { recursive: true, force: true });
        } catch (error) {
          console.warn(chalk.yellow(`Warning: Could not remove ${dir}: ${error.message}`));
        }
      }
    }

    // Clean up environment variables
    delete process.env.ARIEN_TEST_MODE;
    delete process.env.ARIEN_CONFIG_DIR;

    console.log(chalk.green('✅ Cleanup complete'));
  }
}

// Command line interface
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
${chalk.blue.bold('Arien-AI Test Runner')}

Usage: node scripts/test-runner.js [options]

Options:
  --help, -h     Show this help message
  --unit         Run only unit tests
  --integration  Run only integration tests
  --e2e          Run only end-to-end tests

Examples:
  node scripts/test-runner.js              # Run all tests
  node scripts/test-runner.js --unit       # Run only unit tests
  node scripts/test-runner.js --e2e        # Run only E2E tests
`);
  process.exit(0);
}

// Run specific test types if requested
const runner = new TestRunner();

if (args.includes('--unit')) {
  runner.setupTestEnvironment().then(() => runner.runUnitTests()).then(() => runner.cleanup());
} else if (args.includes('--integration')) {
  runner.setupTestEnvironment().then(() => runner.runIntegrationTests()).then(() => runner.cleanup());
} else if (args.includes('--e2e')) {
  runner.setupTestEnvironment().then(() => runner.runE2ETests()).then(() => runner.cleanup());
} else {
  // Run all tests
  runner.run();
}
