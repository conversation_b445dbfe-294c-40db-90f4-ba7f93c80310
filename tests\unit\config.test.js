/**
 * Unit tests for Config class
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { existsSync, unlinkSync, mkdirSync, rmSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { Config } from '../../src/utils/config.js';

describe('Config Unit Tests', () => {
  let config;
  let testConfigDir;

  beforeEach(() => {
    // Create a temporary config directory for testing
    testConfigDir = join(homedir(), '.arien-ai-test');
    if (existsSync(testConfigDir)) {
      rmSync(testConfigDir, { recursive: true, force: true });
    }
    mkdirSync(testConfigDir, { recursive: true });

    // Set environment variable to override config directory
    process.env.ARIEN_CONFIG_DIR = testConfigDir;
    config = new Config();
  });

  afterEach(() => {
    // Clean up test directory
    if (existsSync(testConfigDir)) {
      rmSync(testConfigDir, { recursive: true, force: true });
    }
    // Clean up environment variable
    delete process.env.ARIEN_CONFIG_DIR;
  });

  test('should initialize with default configuration', async () => {
    await config.load();
    
    assert.ok(config.get('version'));
    assert.ok(config.get('llm.providers.deepseek'));
    assert.ok(config.get('llm.providers.ollama'));
    assert.ok(config.get('tools.shell'));
    assert.ok(config.get('tools.web'));
  });

  test('should get nested configuration values', async () => {
    await config.load();
    
    const deepseekConfig = config.get('llm.providers.deepseek');
    assert.ok(deepseekConfig);
    assert.strictEqual(typeof deepseekConfig.enabled, 'boolean');
    
    const shellTimeout = config.get('tools.shell.timeout');
    assert.strictEqual(typeof shellTimeout, 'number');
    assert.ok(shellTimeout > 0);
  });

  test('should set and get configuration values', async () => {
    await config.load();
    
    config.set('test.value', 'hello world');
    assert.strictEqual(config.get('test.value'), 'hello world');
    
    config.set('test.nested.deep', 42);
    assert.strictEqual(config.get('test.nested.deep'), 42);
  });

  test('should return undefined for non-existent paths', async () => {
    await config.load();
    
    assert.strictEqual(config.get('non.existent.path'), undefined);
  });

  test('should validate provider availability', async () => {
    await config.load();
    
    // Test with valid providers
    assert.strictEqual(typeof config.isProviderEnabled('deepseek'), 'boolean');
    assert.strictEqual(typeof config.isProviderEnabled('ollama'), 'boolean');
    
    // Test with invalid provider
    assert.strictEqual(config.isProviderEnabled('invalid'), false);
  });

  test('should get LLM configuration', async () => {
    await config.load();
    
    const llmConfig = config.getLLMConfig();
    assert.ok(llmConfig);
    assert.ok(llmConfig.providers);
    assert.ok(llmConfig.providers.deepseek);
    assert.ok(llmConfig.providers.ollama);
  });

  test('should get tool configuration', async () => {
    await config.load();
    
    const shellConfig = config.getToolConfig('shell');
    assert.ok(shellConfig);
    assert.strictEqual(typeof shellConfig.enabled, 'boolean');
    assert.strictEqual(typeof shellConfig.timeout, 'number');
    
    const webConfig = config.getToolConfig('web');
    assert.ok(webConfig);
    assert.strictEqual(typeof webConfig.enabled, 'boolean');
    assert.strictEqual(typeof webConfig.timeout, 'number');
  });

  test('should get retry configuration', async () => {
    await config.load();
    
    const retryConfig = config.getRetryConfig();
    assert.ok(retryConfig);
    assert.strictEqual(typeof retryConfig.maxAttempts, 'number');
    assert.strictEqual(typeof retryConfig.baseDelay, 'number');
    assert.strictEqual(typeof retryConfig.maxDelay, 'number');
    assert.ok(retryConfig.maxAttempts > 0);
    assert.ok(retryConfig.baseDelay > 0);
    assert.ok(retryConfig.maxDelay > 0);
  });

  test('should get UI configuration', async () => {
    await config.load();
    
    const uiConfig = config.getUIConfig();
    assert.ok(uiConfig);
    assert.strictEqual(typeof uiConfig.animations, 'boolean');
    assert.strictEqual(typeof uiConfig.colors, 'boolean');
    assert.strictEqual(typeof uiConfig.progressIndicator, 'string');
  });

  test('should get logging configuration', async () => {
    await config.load();
    
    const loggingConfig = config.getLoggingConfig();
    assert.ok(loggingConfig);
    assert.strictEqual(typeof loggingConfig.level, 'string');
    assert.strictEqual(typeof loggingConfig.console, 'boolean');
    assert.ok(['error', 'warn', 'info', 'debug'].includes(loggingConfig.level));
  });

  test('should handle missing config file gracefully', async () => {
    // Don't create config file, should use defaults
    await config.load();
    
    assert.ok(config.get('version'));
    assert.ok(config.get('llm'));
    assert.ok(config.get('tools'));
  });

  test('should save configuration to file', async () => {
    await config.load();

    config.set('test.saved', 'value');
    await config.save();

    const configPath = config.getConfigPath();
    assert.ok(existsSync(configPath));

    // Load new config instance to verify persistence
    process.env.ARIEN_CONFIG_DIR = testConfigDir;
    const newConfig = new Config();
    await newConfig.load();

    assert.strictEqual(newConfig.get('test.saved'), 'value');
  });

  test('should reset to default configuration', async () => {
    await config.load();
    
    config.set('test.custom', 'value');
    assert.strictEqual(config.get('test.custom'), 'value');
    
    await config.reset();
    assert.strictEqual(config.get('test.custom'), undefined);
    assert.ok(config.get('version')); // Should still have defaults
  });

  test('should get all configuration', async () => {
    await config.load();
    
    const allConfig = config.getAll();
    assert.ok(allConfig);
    assert.strictEqual(typeof allConfig, 'object');
    assert.ok(allConfig.version);
    assert.ok(allConfig.llm);
    assert.ok(allConfig.tools);
  });

  test('should return config path', async () => {
    const configPath = config.getConfigPath();
    assert.strictEqual(typeof configPath, 'string');
    assert.ok(configPath.length > 0);
  });
});
