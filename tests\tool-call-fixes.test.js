/**
 * Test for tool call format fixes
 */

import { test, describe } from 'node:test';
import assert from 'node:assert';
import { DeepseekProvider } from '../src/llm/providers/deepseek-provider.js';
import { Config } from '../src/utils/config.js';
import { Logger } from '../src/utils/logger.js';

describe('Tool Call Format Fixes', () => {
  let config, logger;

  // Setup
  test('Setup test environment', async () => {
    config = new Config();
    await config.load();
    logger = new Logger(config);
    assert.ok(config);
    assert.ok(logger);
  });

  test('Deepseek provider should handle tool calls in messages correctly', () => {
    const provider = new DeepseekProvider(config, logger);
    
    // Test messages with tool calls
    const messages = [
      {
        role: 'user',
        content: 'List files in current directory'
      },
      {
        role: 'assistant',
        content: 'I need to use some tools to help you.',
        tool_calls: [
          {
            id: 'call_123',
            type: 'function',
            function: {
              name: 'shell',
              arguments: { command: 'ls -la' }
            }
          }
        ]
      },
      {
        role: 'function',
        name: 'shell',
        content: 'total 8\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 ..'
      }
    ];

    // This should not throw an error
    const preparedMessages = provider.prepareMessages(messages);
    
    assert.ok(Array.isArray(preparedMessages));
    assert.strictEqual(preparedMessages.length, 3);
    
    // Check that function message was converted to user message
    assert.strictEqual(preparedMessages[2].role, 'user');
    assert.ok(preparedMessages[2].content.includes('Tool "shell" result:'));
    
    // Check that tool_calls are properly formatted
    const assistantMessage = preparedMessages[1];
    assert.ok(assistantMessage.tool_calls);
    assert.strictEqual(assistantMessage.tool_calls.length, 1);
    
    const toolCall = assistantMessage.tool_calls[0];
    assert.strictEqual(toolCall.id, 'call_123');
    assert.strictEqual(toolCall.type, 'function');
    assert.strictEqual(toolCall.function.name, 'shell');
    assert.strictEqual(typeof toolCall.function.arguments, 'string');
    
    // Arguments should be stringified JSON
    const parsedArgs = JSON.parse(toolCall.function.arguments);
    assert.strictEqual(parsedArgs.command, 'ls -la');
  });

  test('Deepseek provider should handle malformed tool calls gracefully', () => {
    const provider = new DeepseekProvider(config, logger);
    
    const messages = [
      {
        role: 'assistant',
        content: 'Testing malformed tool calls',
        tool_calls: [
          {
            id: 'call_123',
            type: 'function',
            function: {
              name: 'shell',
              arguments: { command: 'ls -la' }
            }
          },
          {
            // Missing id and function name - should be filtered out
            type: 'function',
            function: {
              arguments: { invalid: 'call' }
            }
          },
          {
            id: 'call_456',
            type: 'function',
            function: {
              name: 'web',
              arguments: 'already a string'
            }
          }
        ]
      }
    ];

    const preparedMessages = provider.prepareMessages(messages);
    
    assert.ok(Array.isArray(preparedMessages));
    assert.strictEqual(preparedMessages.length, 1);
    
    const assistantMessage = preparedMessages[0];
    assert.ok(assistantMessage.tool_calls);
    
    // Should have filtered out the malformed tool call
    assert.strictEqual(assistantMessage.tool_calls.length, 2);
    
    // Check valid tool calls
    assert.strictEqual(assistantMessage.tool_calls[0].id, 'call_123');
    assert.strictEqual(assistantMessage.tool_calls[1].id, 'call_456');
    
    // Check that string arguments are preserved
    assert.strictEqual(assistantMessage.tool_calls[1].function.arguments, 'already a string');
  });

  test('Base provider message validation should handle tool calls', async () => {
    const { BaseLLMProvider } = await import('../src/llm/providers/base-provider.js');
    
    class TestProvider extends BaseLLMProvider {
      constructor() {
        super('test', config, logger);
      }
      
      async initialize() {}
      async checkAvailability() {}
      async generateCompletion() {}
      async generateCompletionWithTools() {}
      async getAvailableModels() {}
    }
    
    const provider = new TestProvider();
    
    // Valid messages with tool calls
    const validMessages = [
      {
        role: 'user',
        content: 'Test message'
      },
      {
        role: 'assistant',
        content: 'I need to use tools',
        tool_calls: [
          {
            id: 'call_123',
            function: {
              name: 'shell',
              arguments: '{"command": "ls"}'
            }
          }
        ]
      },
      {
        role: 'function',
        name: 'shell',
        content: 'file1.txt\nfile2.txt'
      }
    ];
    
    // Should not throw
    assert.ok(provider.validateMessages(validMessages));
    
    // Invalid messages - tool call without id
    const invalidMessages = [
      {
        role: 'assistant',
        content: 'Test',
        tool_calls: [
          {
            function: {
              name: 'shell',
              arguments: '{"command": "ls"}'
            }
          }
        ]
      }
    ];
    
    // Should throw
    assert.throws(() => {
      provider.validateMessages(invalidMessages);
    }, /must have an id/);
  });

  test('Message validation should provide helpful error messages', async () => {
    const { BaseLLMProvider } = await import('../src/llm/providers/base-provider.js');
    
    class TestProvider extends BaseLLMProvider {
      constructor() {
        super('test', config, logger);
      }
      
      async initialize() {}
      async checkAvailability() {}
      async generateCompletion() {}
      async generateCompletionWithTools() {}
      async getAvailableModels() {}
    }
    
    const provider = new TestProvider();
    
    // Test various invalid message formats
    const testCases = [
      {
        messages: [{ role: 'user' }],
        expectedError: /must have content/
      },
      {
        messages: [{ role: 'function', content: 'test' }],
        expectedError: /must have a name/
      },
      {
        messages: [{ role: 'assistant', tool_calls: [{ function: { name: 'test' } }] }],
        expectedError: /must have an id/
      },
      {
        messages: [{ role: 'assistant', tool_calls: [{ id: 'test' }] }],
        expectedError: /must have function\.name/
      }
    ];
    
    for (const testCase of testCases) {
      assert.throws(() => {
        provider.validateMessages(testCase.messages);
      }, testCase.expectedError);
    }
  });
});
