/**
 * <PERSON>rro<PERSON> - Comprehensive error management and recovery
 */

import chalk from 'chalk';

export class ErrorHandler {
  #config;
  #logger;
  #errorCounts = new Map();
  #lastErrors = new Map();

  constructor(config, logger) {
    this.#config = config;
    this.#logger = logger;
  }

  /**
   * Handle and categorize errors with appropriate responses
   */
  handleError(error, context = {}) {
    const errorInfo = this.#analyzeError(error, context);
    
    // Log the error
    this.#logError(errorInfo);
    
    // Track error frequency
    this.#trackError(errorInfo);
    
    // Generate user-friendly response
    const response = this.#generateErrorResponse(errorInfo);
    
    return {
      success: false,
      error: response,
      errorType: errorInfo.type,
      recoverable: errorInfo.recoverable,
      suggestions: errorInfo.suggestions,
      context: context,
      sessionId: context.sessionId
    };
  }

  /**
   * Analyze error and categorize it
   */
  #analyzeError(error, context) {
    const errorInfo = {
      originalError: error,
      message: error.message || 'Unknown error',
      type: 'unknown',
      category: 'system',
      severity: 'medium',
      recoverable: false,
      suggestions: [],
      context
    };

    // Network errors
    if (this.#isNetworkError(error)) {
      errorInfo.type = 'network';
      errorInfo.category = 'connectivity';
      errorInfo.severity = 'medium';
      errorInfo.recoverable = true;
      errorInfo.suggestions = [
        'Check your internet connection',
        'Verify the URL or endpoint is correct',
        'Try again in a few moments',
        'Check if the service is experiencing downtime'
      ];
    }
    
    // Rate limiting errors
    else if (this.#isRateLimitError(error)) {
      errorInfo.type = 'rate_limit';
      errorInfo.category = 'api';
      errorInfo.severity = 'low';
      errorInfo.recoverable = true;
      errorInfo.suggestions = [
        'Wait a moment before trying again',
        'Consider using a different provider',
        'Check your API usage limits'
      ];
    }
    
    // Authentication errors
    else if (this.#isAuthError(error)) {
      errorInfo.type = 'authentication';
      errorInfo.category = 'security';
      errorInfo.severity = 'high';
      errorInfo.recoverable = false;
      errorInfo.suggestions = [
        'Check your API key configuration',
        'Verify your credentials are correct',
        'Ensure your account has proper permissions',
        'Run "arien config --init" to reconfigure'
      ];
    }
    
    // Permission errors
    else if (this.#isPermissionError(error)) {
      errorInfo.type = 'permission';
      errorInfo.category = 'security';
      errorInfo.severity = 'medium';
      errorInfo.recoverable = false;
      errorInfo.suggestions = [
        'Check file/directory permissions',
        'Try running with appropriate privileges',
        'Verify you have access to the requested resource'
      ];
    }
    
    // Command not found errors
    else if (this.#isCommandNotFoundError(error)) {
      errorInfo.type = 'command_not_found';
      errorInfo.category = 'system';
      errorInfo.severity = 'medium';
      errorInfo.recoverable = false;
      errorInfo.suggestions = [
        'Check if the command is installed',
        'Verify the command name is correct',
        'Check your PATH environment variable',
        'Try using the full path to the command'
      ];
    }
    
    // Timeout errors
    else if (this.#isTimeoutError(error)) {
      errorInfo.type = 'timeout';
      errorInfo.category = 'performance';
      errorInfo.severity = 'medium';
      errorInfo.recoverable = true;
      errorInfo.suggestions = [
        'Try increasing the timeout value',
        'Check if the operation is taking longer than expected',
        'Consider breaking the task into smaller parts'
      ];
    }
    
    // Configuration errors
    else if (this.#isConfigError(error)) {
      errorInfo.type = 'configuration';
      errorInfo.category = 'setup';
      errorInfo.severity = 'high';
      errorInfo.recoverable = false;
      errorInfo.suggestions = [
        'Check your configuration file',
        'Run "arien config --init" to reset configuration',
        'Verify all required settings are present'
      ];
    }
    
    // LLM provider errors
    else if (this.#isLLMError(error)) {
      errorInfo.type = 'llm_provider';
      errorInfo.category = 'ai';
      errorInfo.severity = 'medium';
      errorInfo.recoverable = true;
      errorInfo.suggestions = [
        'Try using a different LLM provider',
        'Check if the model is available',
        'Verify your API quota and limits'
      ];
    }

    return errorInfo;
  }

  /**
   * Generate user-friendly error response
   */
  #generateErrorResponse(errorInfo) {
    const { type, message, suggestions } = errorInfo;
    
    let response = `❌ ${this.#getErrorTitle(type)}\n\n`;
    response += `${message}\n\n`;
    
    if (suggestions.length > 0) {
      response += `💡 Suggestions:\n`;
      suggestions.forEach((suggestion, index) => {
        response += `${index + 1}. ${suggestion}\n`;
      });
    }
    
    // Add recovery information
    if (errorInfo.recoverable) {
      response += `\n🔄 This error is temporary and may resolve itself. You can try again.`;
    } else {
      response += `\n⚠️  This error requires manual intervention to resolve.`;
    }
    
    return response;
  }

  /**
   * Get error title based on type
   */
  #getErrorTitle(type) {
    const titles = {
      network: 'Network Connection Error',
      rate_limit: 'Rate Limit Exceeded',
      authentication: 'Authentication Failed',
      permission: 'Permission Denied',
      command_not_found: 'Command Not Found',
      timeout: 'Operation Timed Out',
      configuration: 'Configuration Error',
      llm_provider: 'AI Provider Error',
      unknown: 'Unexpected Error'
    };
    
    return titles[type] || titles.unknown;
  }

  /**
   * Log error with appropriate level
   */
  #logError(errorInfo) {
    const logLevel = this.#getLogLevel(errorInfo.severity);
    const logData = {
      type: errorInfo.type,
      category: errorInfo.category,
      severity: errorInfo.severity,
      recoverable: errorInfo.recoverable,
      context: errorInfo.context
    };
    
    this.#logger[logLevel](`Error: ${errorInfo.message}`, logData);
  }

  /**
   * Track error frequency for pattern detection
   */
  #trackError(errorInfo) {
    const errorKey = `${errorInfo.type}:${errorInfo.category}`;
    const count = this.#errorCounts.get(errorKey) || 0;
    
    this.#errorCounts.set(errorKey, count + 1);
    this.#lastErrors.set(errorKey, {
      timestamp: Date.now(),
      message: errorInfo.message,
      context: errorInfo.context
    });
    
    // Alert if error is happening frequently
    if (count > 5) {
      this.#logger.warn(`Frequent error detected: ${errorKey}`, {
        count: count + 1,
        lastOccurrence: this.#lastErrors.get(errorKey)
      });
    }
  }

  /**
   * Get appropriate log level for error severity
   */
  #getLogLevel(severity) {
    switch (severity) {
      case 'low': return 'debug';
      case 'medium': return 'warn';
      case 'high': return 'error';
      default: return 'error';
    }
  }

  /**
   * Error type detection methods
   */
  #isNetworkError(error) {
    const networkCodes = ['ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'ENETUNREACH'];
    return networkCodes.includes(error.code) || 
           error.message?.includes('network') ||
           error.message?.includes('connection');
  }

  #isRateLimitError(error) {
    return error.response?.status === 429 ||
           error.message?.includes('rate limit') ||
           error.message?.includes('too many requests');
  }

  #isAuthError(error) {
    return error.response?.status === 401 ||
           error.response?.status === 403 ||
           error.message?.includes('unauthorized') ||
           error.message?.includes('authentication') ||
           error.message?.includes('invalid api key');
  }

  #isPermissionError(error) {
    return error.code === 'EACCES' ||
           error.code === 'EPERM' ||
           error.message?.includes('permission denied') ||
           error.message?.includes('access denied');
  }

  #isCommandNotFoundError(error) {
    return error.code === 'ENOENT' ||
           error.message?.includes('command not found') ||
           error.message?.includes('not recognized');
  }

  #isTimeoutError(error) {
    return error.code === 'ETIMEDOUT' ||
           error.message?.includes('timeout') ||
           error.message?.includes('timed out');
  }

  #isConfigError(error) {
    return error.message?.includes('configuration') ||
           error.message?.includes('config') ||
           error.message?.includes('invalid setting');
  }

  #isLLMError(error) {
    return error.message?.includes('model') ||
           error.message?.includes('provider') ||
           error.message?.includes('completion');
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {
      totalErrors: 0,
      errorsByType: {},
      errorsByCategory: {},
      recentErrors: []
    };
    
    for (const [key, count] of this.#errorCounts) {
      const [type, category] = key.split(':');
      stats.totalErrors += count;
      stats.errorsByType[type] = (stats.errorsByType[type] || 0) + count;
      stats.errorsByCategory[category] = (stats.errorsByCategory[category] || 0) + count;
    }
    
    // Get recent errors (last 10)
    const recentEntries = Array.from(this.#lastErrors.entries())
      .sort((a, b) => b[1].timestamp - a[1].timestamp)
      .slice(0, 10);
    
    stats.recentErrors = recentEntries.map(([key, error]) => ({
      type: key.split(':')[0],
      category: key.split(':')[1],
      message: error.message,
      timestamp: new Date(error.timestamp).toISOString()
    }));
    
    return stats;
  }

  /**
   * Clear error tracking data
   */
  clearErrorStats() {
    this.#errorCounts.clear();
    this.#lastErrors.clear();
    this.#logger.info('Error statistics cleared');
  }
}
