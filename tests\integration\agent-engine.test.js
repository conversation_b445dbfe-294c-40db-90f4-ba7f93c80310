/**
 * Integration tests for Agent Engine
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { existsSync, rmSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { AgentEngine } from '../../src/core/agent-engine.js';
import { Config } from '../../src/utils/config.js';
import { Logger } from '../../src/utils/logger.js';

describe('Agent Engine Integration Tests', () => {
  let agentEngine;
  let config;
  let logger;
  let testDir;

  beforeEach(async () => {
    // Create test environment
    testDir = join(homedir(), '.arien-ai-test-integration');
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
    mkdirSync(testDir, { recursive: true });

    // Setup config with test settings
    config = new Config();
    await config.load();
    
    // Disable actual LLM calls for testing
    config.set('llm.providers.deepseek.enabled', false);
    config.set('llm.providers.ollama.enabled', false);
    config.set('logging.file', join(testDir, 'test.log'));
    config.set('logging.console', false);

    logger = new Logger(config);
    agentEngine = new AgentEngine(config, logger);
  });

  afterEach(() => {
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  test('should initialize agent engine', async () => {
    await agentEngine.initialize();
    
    const status = await agentEngine.getStatus();
    assert.ok(status);
    assert.ok(status.initialized);
    assert.ok(status.tools);
    assert.ok(Array.isArray(status.tools));
  });

  test('should get system status', async () => {
    await agentEngine.initialize();
    
    const status = await agentEngine.getStatus();
    assert.ok(status);
    assert.strictEqual(typeof status.initialized, 'boolean');
    assert.ok(status.configPath);
    assert.ok(status.logLevel);
    assert.ok(status.providers);
    assert.ok(status.tools);
    assert.ok(Array.isArray(status.tools));
  });

  test('should handle user input processing', async () => {
    await agentEngine.initialize();

    // Test with simple input that doesn't require LLM
    try {
      const result = await agentEngine.processUserInput('help', {
        interactive: false
      });

      assert.ok(result);
      assert.strictEqual(typeof result.success, 'boolean');
      assert.ok(result.sessionId);
      if (result.duration !== undefined) {
        assert.strictEqual(typeof result.duration, 'number');
      }
    } catch (error) {
      // Expected to fail when no LLM providers are available
      assert.ok(error.message);
    }
  });

  test('should validate input sanitization', async () => {
    await agentEngine.initialize();
    
    // Test with potentially dangerous input
    const maliciousInput = 'rm -rf /\x00\x01\x02';
    
    try {
      await agentEngine.processUserInput(maliciousInput);
      // Should not reach here if properly sanitized
    } catch (error) {
      // Expected to fail due to sanitization or safety checks
      assert.ok(error.message);
    }
  });

  test('should handle empty input gracefully', async () => {
    await agentEngine.initialize();
    
    try {
      await agentEngine.processUserInput('');
    } catch (error) {
      assert.ok(error.message.includes('empty') || error.message.includes('invalid'));
    }
    
    try {
      await agentEngine.processUserInput('   ');
    } catch (error) {
      assert.ok(error.message.includes('empty') || error.message.includes('invalid'));
    }
  });

  test('should handle very long input', async () => {
    await agentEngine.initialize();
    
    const longInput = 'a'.repeat(20000);
    
    try {
      await agentEngine.processUserInput(longInput);
    } catch (error) {
      assert.ok(error.message.includes('long') || error.message.includes('limit'));
    }
  });

  test('should maintain conversation history', async () => {
    await agentEngine.initialize();
    
    // Process multiple inputs
    await agentEngine.processUserInput('first message');
    await agentEngine.processUserInput('second message');
    
    const history = agentEngine.getConversationHistory();
    assert.ok(Array.isArray(history));
    assert.ok(history.length >= 2);
  });

  test('should clear conversation history', async () => {
    await agentEngine.initialize();
    
    await agentEngine.processUserInput('test message');
    let history = agentEngine.getConversationHistory();
    assert.ok(history.length > 0);
    
    agentEngine.clearConversationHistory();
    history = agentEngine.getConversationHistory();
    assert.strictEqual(history.length, 0);
  });

  test('should handle workflow execution', async () => {
    await agentEngine.initialize();
    
    // Test built-in workflow
    try {
      const result = await agentEngine.executeWorkflow('system-info');
      assert.ok(result);
      assert.strictEqual(typeof result.success, 'boolean');
      assert.ok(result.workflow);
    } catch (error) {
      // May fail if tools are not available, which is expected in test environment
      assert.ok(error.message);
    }
  });

  test('should handle non-existent workflow', async () => {
    await agentEngine.initialize();
    
    try {
      await agentEngine.executeWorkflow('non-existent-workflow');
      assert.fail('Should have thrown error for non-existent workflow');
    } catch (error) {
      assert.ok(error.message.includes('not found') || error.message.includes('Workflow'));
    }
  });

  test('should get available tools', async () => {
    await agentEngine.initialize();
    
    const tools = agentEngine.getAvailableTools();
    assert.ok(Array.isArray(tools));
    
    // Should have at least shell and web tools
    const toolNames = tools.map(t => t.function.name);
    assert.ok(toolNames.includes('shell'));
    assert.ok(toolNames.includes('web'));
  });

  test('should handle session management', async () => {
    await agentEngine.initialize();
    
    const sessionId1 = 'session-1';
    const sessionId2 = 'session-2';
    
    const result1 = await agentEngine.processUserInput('test 1', { sessionId: sessionId1 });
    const result2 = await agentEngine.processUserInput('test 2', { sessionId: sessionId2 });
    
    assert.strictEqual(result1.sessionId, sessionId1);
    assert.strictEqual(result2.sessionId, sessionId2);
  });

  test('should handle concurrent requests', async () => {
    await agentEngine.initialize();
    
    const promises = [
      agentEngine.processUserInput('request 1'),
      agentEngine.processUserInput('request 2'),
      agentEngine.processUserInput('request 3')
    ];
    
    const results = await Promise.allSettled(promises);
    
    // All requests should complete (either resolve or reject)
    assert.strictEqual(results.length, 3);
    results.forEach(result => {
      assert.ok(result.status === 'fulfilled' || result.status === 'rejected');
    });
  });

  test('should handle initialization errors gracefully', async () => {
    // Create agent with invalid config
    const invalidConfig = new Config();
    await invalidConfig.load();
    invalidConfig.set('llm.providers.deepseek.baseUrl', 'invalid-url');
    
    const invalidLogger = new Logger(invalidConfig);
    const invalidAgent = new AgentEngine(invalidConfig, invalidLogger);
    
    // Should not throw during initialization, but may log warnings
    await invalidAgent.initialize();
    
    const status = await invalidAgent.getStatus();
    assert.ok(status);
  });

  test('should provide meaningful error messages', async () => {
    await agentEngine.initialize();
    
    try {
      await agentEngine.processUserInput(null);
    } catch (error) {
      assert.ok(error.message);
      assert.strictEqual(typeof error.message, 'string');
      assert.ok(error.message.length > 0);
    }
  });
});
